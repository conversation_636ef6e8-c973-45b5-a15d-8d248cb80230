import requests
import pandas as pd
import json
from datetime import datetime, timedelta
import time
import os

class NiftyClosingPricesFetcher:
    def __init__(self):
        """Initialize the data fetcher"""
        self.load_config()
        self.headers = {
            "access-token": self.api_config.get("access_token", ""),
            "client-id": self.api_config.get("client_id", ""),
            "Content-Type": "application/json"
        }
        
        # All 5-minute time slots for full trading day
        self.time_slots = [
            "09:15", "09:20", "09:25", "09:30", "09:35", "09:40", "09:45", "09:50", "09:55", 
            "10:00", "10:05", "10:10", "10:15", "10:20", "10:25", "10:30", "10:35", "10:40", "10:45", "10:50", "10:55",
            "11:00", "11:05", "11:10", "11:15", "11:20", "11:25", "11:30", "11:35", "11:40", "11:45", "11:50", "11:55",
            "12:00", "12:05", "12:10", "12:15", "12:20", "12:25", "12:30", "12:35", "12:40", "12:45", "12:50", "12:55",
            "13:00", "13:05", "13:10", "13:15", "13:20", "13:25", "13:30", "13:35", "13:40", "13:45", "13:50", "13:55",
            "14:00", "14:05", "14:10", "14:15", "14:20", "14:25", "14:30", "14:35", "14:40", "14:45", "14:50", "14:55",
            "15:00", "15:05", "15:10", "15:15", "15:20", "15:25", "15:30"
        ]
        
    def load_config(self):
        """Load API configuration"""
        try:
            with open('dhan_config.json') as config_file:
                self.api_config = json.load(config_file)
        except Exception as e:
            print(f"Configuration error: {e}")
            exit(1)
    
    def verify_credentials(self):
        """Verify API credentials"""
        profile_url = "https://api.dhan.co/v2/profile"
        try:
            response = requests.get(profile_url, headers=self.headers)
            return response.status_code == 200
        except:
            return False
    
    def generate_date_ranges(self, start_date, end_date):
        """Generate 60-day chunks for API requests (smaller chunks to avoid rate limits)"""
        ranges = []
        current_date = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        while current_date < end_dt:
            # Use 60-day chunks instead of 90 to be more conservative
            chunk_end = min(current_date + timedelta(days=59), end_dt)
            ranges.append({
                "from_date": current_date.strftime("%Y-%m-%d 09:15:00"),
                "to_date": chunk_end.strftime("%Y-%m-%d 15:30:00"),
                "start_date": current_date.strftime("%Y-%m-%d"),
                "end_date": chunk_end.strftime("%Y-%m-%d")
            })
            current_date = chunk_end + timedelta(days=1)
        
        return ranges
    
    def fetch_data_chunk(self, from_date, to_date):
        """Fetch data for a specific date range with retry logic"""
        url = "https://api.dhan.co/v2/charts/intraday"
        payload = {
            "securityId": "13",
            "exchangeSegment": "IDX_I",
            "instrument": "INDEX",
            "interval": "5",
            "fromDate": from_date,
            "toDate": to_date
        }
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.post(url, headers=self.headers, json=payload)
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:  # Rate limit
                    print(f"    Rate limit hit, waiting 30 seconds...")
                    time.sleep(30)
                    continue
                else:
                    print(f"    API Error: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(10)
                        continue
            except Exception as e:
                print(f"    Request error: {e}")
                if attempt < max_retries - 1:
                    time.sleep(10)
                    continue
        
        return None
    
    def process_and_format_data(self, raw_data):
        """Process raw data and format for closing prices"""
        if not raw_data or not all(k in raw_data for k in ['close', 'timestamp']):
            return None
            
        df = pd.DataFrame({
            'timestamp': raw_data['timestamp'],
            'close': raw_data['close']
        })
        
        # Convert to IST datetime
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s') + timedelta(hours=5, minutes=30)
        df['date'] = df['datetime'].dt.date
        df['time'] = df['datetime'].dt.strftime('%H:%M')
        
        # Create pivot table with dates as rows and times as columns
        pivot_df = df.pivot_table(index='date', columns='time', values='close', aggfunc='first')
        pivot_df = pivot_df.reset_index()
        
        # Ensure we have all the time columns (fill missing with NaN)
        for time_slot in self.time_slots:
            if time_slot not in pivot_df.columns:
                pivot_df[time_slot] = None
        
        # Reorder columns: Date first, then all time slots in order
        column_order = ['date'] + self.time_slots
        pivot_df = pivot_df.reindex(columns=column_order)
        
        # Rename date column
        pivot_df = pivot_df.rename(columns={'date': 'Date'})
        
        return pivot_df
    
    def save_progress(self, data, filename):
        """Save progress to file"""
        if data is not None and not data.empty:
            data.to_csv(filename, index=False)
            return True
        return False
    
    def load_existing_progress(self, filename):
        """Load existing progress if available"""
        if os.path.exists(filename):
            try:
                return pd.read_csv(filename)
            except:
                return None
        return None
    
    def run(self):
        """Main execution flow"""
        print("\n===== NIFTY Closing Prices Fetcher =====")
        print("Fetching CLOSING PRICES from January 2022 to August 2025...")
        print("This will take several hours due to API rate limits.")
        
        if not self.verify_credentials():
            print("✗ Credential verification failed")
            return
        
        print("✓ Credentials verified")
        
        # Generate date ranges
        date_ranges = self.generate_date_ranges("2022-01-01", "2025-08-31")
        print(f"Will fetch data in {len(date_ranges)} chunks (60-day chunks)")
        
        # Check for existing progress
        final_output_file = "nifty_closing_prices_2022_2025.csv"
        progress_file = "nifty_closing_progress.csv"
        
        existing_data = self.load_existing_progress(progress_file)
        if existing_data is not None:
            print(f"Found existing progress with {len(existing_data)} days")
            all_dataframes = [existing_data]
            start_chunk = len(existing_data) // 30  # Rough estimate of where to resume
        else:
            all_dataframes = []
            start_chunk = 0
        
        total_days_fetched = len(existing_data) if existing_data is not None else 0
        
        for i in range(start_chunk, len(date_ranges)):
            date_range = date_ranges[i]
            print(f"\nChunk {i+1}/{len(date_ranges)}: {date_range['start_date']} to {date_range['end_date']}")
            
            raw_data = self.fetch_data_chunk(date_range['from_date'], date_range['to_date'])
            if raw_data:
                processed_df = self.process_and_format_data(raw_data)
                if processed_df is not None and not processed_df.empty:
                    all_dataframes.append(processed_df)
                    total_days_fetched += len(processed_df)
                    print(f"✓ Got {len(processed_df)} trading days (Total: {total_days_fetched})")
                else:
                    print("✗ No data in this chunk")
            else:
                print("✗ Failed to fetch data for this chunk")
            
            # Save progress every 5 chunks
            if (i + 1) % 5 == 0 and all_dataframes:
                combined_data = pd.concat(all_dataframes, ignore_index=True)
                combined_data = combined_data.drop_duplicates(subset=['Date'], keep='last')
                self.save_progress(combined_data, progress_file)
                print(f"Progress saved: {len(combined_data)} days")
            
            # Conservative delay to avoid rate limits
            print("Waiting 10 seconds to avoid rate limits...")
            time.sleep(10)
        
        if not all_dataframes:
            print("No data was fetched!")
            return
        
        # Combine all data and remove duplicates
        print("\nCombining all data...")
        combined_data = pd.concat(all_dataframes, ignore_index=True)
        combined_data = combined_data.drop_duplicates(subset=['Date'], keep='last')
        combined_data = combined_data.sort_values('Date').reset_index(drop=True)
        
        # Save final file
        combined_data.to_csv(final_output_file, index=False)
        
        print(f"\n✅ SUCCESS! Data saved to {final_output_file}")
        print(f"📅 Date range: {combined_data['Date'].min()} to {combined_data['Date'].max()}")
        print(f"📊 Total trading days: {len(combined_data)}")
        print(f"📈 Columns: Date + {len(self.time_slots)} time slots (5-min closing prices)")
        print("\n===== Complete =====")

if __name__ == "__main__":
    NiftyClosingPricesFetcher().run()
