import requests
import pandas as pd
import json

def find_sensex_in_master():
    """Search for SENSEX in the Dhan instrument master file"""
    print("🔍 Searching for SENSEX in Dhan instrument master file...")
    
    try:
        # Download the instrument master CSV
        url = "https://images.dhan.co/api-data/api-scrip-master.csv"
        response = requests.get(url)
        
        if response.status_code == 200:
            # Save to temporary file
            with open('temp_master.csv', 'w') as f:
                f.write(response.text)
            
            # Read CSV
            df = pd.read_csv('temp_master.csv')
            
            print(f"📊 Total instruments in master file: {len(df)}")
            print(f"📋 Columns: {list(df.columns)}")
            
            # Search for SENSEX-related entries
            sensex_keywords = ['SENSEX', 'BSE', 'BSESENSEX', 'BSE30', 'SENSEX30']
            
            print(f"\n🔍 Searching for SENSEX-related instruments...")
            
            for keyword in sensex_keywords:
                print(f"\n--- Searching for '{keyword}' ---")
                
                # Search in trading symbol
                matches_symbol = df[df['SEM_TRADING_SYMBOL'].str.contains(keyword, case=False, na=False)]
                if not matches_symbol.empty:
                    print(f"Found {len(matches_symbol)} matches in trading symbol:")
                    for _, row in matches_symbol.head(10).iterrows():
                        print(f"  ID: {row['SEM_SMST_SECURITY_ID']}, Symbol: {row['SEM_TRADING_SYMBOL']}, Exchange: {row['SEM_EXM_EXCH_ID']}")
                
                # Search in instrument name
                matches_name = df[df['SEM_INSTRUMENT_NAME'].str.contains(keyword, case=False, na=False)]
                if not matches_name.empty:
                    print(f"Found {len(matches_name)} matches in instrument name:")
                    for _, row in matches_name.head(10).iterrows():
                        print(f"  ID: {row['SEM_SMST_SECURITY_ID']}, Name: {row['SEM_INSTRUMENT_NAME']}, Exchange: {row['SEM_EXM_EXCH_ID']}")
                
                # Search in custom symbol
                matches_custom = df[df['SEM_CUSTOM_SYMBOL'].str.contains(keyword, case=False, na=False)]
                if not matches_custom.empty:
                    print(f"Found {len(matches_custom)} matches in custom symbol:")
                    for _, row in matches_custom.head(10).iterrows():
                        print(f"  ID: {row['SEM_SMST_SECURITY_ID']}, Custom: {row['SEM_CUSTOM_SYMBOL']}, Exchange: {row['SEM_EXM_EXCH_ID']}")
            
            # Also search for BSE indices
            print(f"\n--- Searching for BSE indices ---")
            bse_indices = df[(df['SEM_EXM_EXCH_ID'] == 'BSE') & (df['SEM_INSTRUMENT_NAME'].str.contains('INDEX|INDX', case=False, na=False))]
            if not bse_indices.empty:
                print(f"Found {len(bse_indices)} BSE indices:")
                for _, row in bse_indices.head(20).iterrows():
                    print(f"  ID: {row['SEM_SMST_SECURITY_ID']}, Symbol: {row['SEM_TRADING_SYMBOL']}, Name: {row['SEM_INSTRUMENT_NAME']}")
            
            # Search for any BSE instruments with INDEX type
            print(f"\n--- All BSE INDEX instruments ---")
            bse_all_index = df[(df['SEM_EXM_EXCH_ID'] == 'BSE') & (df['SEM_EXCH_INSTRUMENT_TYPE'] == 'INDEX')]
            if not bse_all_index.empty:
                print(f"Found {len(bse_all_index)} BSE INDEX instruments:")
                for _, row in bse_all_index.head(20).iterrows():
                    print(f"  ID: {row['SEM_SMST_SECURITY_ID']}, Symbol: {row['SEM_TRADING_SYMBOL']}")
            
            # Clean up
            import os
            os.remove('temp_master.csv')
            
        else:
            print(f"❌ Failed to download instrument master file: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    find_sensex_in_master()
