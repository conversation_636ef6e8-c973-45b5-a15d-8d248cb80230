import requests
import json
import time

def test_sensex_endpoints():
    """Test different API endpoints and parameters for SENSEX"""
    
    # Load config
    with open('dhan_config.json') as f:
        config = json.load(f)
    
    headers = {
        "access-token": config["access_token"],
        "client-id": config["client_id"],
        "Content-Type": "application/json"
    }
    
    print("🔍 Testing different SENSEX API configurations...")
    
    # Test configurations
    test_configs = [
        {
            "name": "SENSEX Intraday - BSE",
            "url": "https://api.dhan.co/v2/charts/intraday",
            "payload": {
                "securityId": "51",
                "exchangeSegment": "BSE",
                "instrument": "INDEX",
                "interval": "5",
                "fromDate": "2024-08-01 09:15:00",
                "toDate": "2024-08-01 15:30:00"
            }
        },
        {
            "name": "SENSEX Historical - BSE",
            "url": "https://api.dhan.co/v2/charts/historical",
            "payload": {
                "securityId": "51",
                "exchangeSegment": "BSE",
                "instrument": "INDEX",
                "fromDate": "2024-08-01",
                "toDate": "2024-08-01"
            }
        },
        {
            "name": "SENSEX Intraday - BSE_EQ",
            "url": "https://api.dhan.co/v2/charts/intraday",
            "payload": {
                "securityId": "51",
                "exchangeSegment": "BSE_EQ",
                "instrument": "INDEX",
                "interval": "5",
                "fromDate": "2024-08-01 09:15:00",
                "toDate": "2024-08-01 15:30:00"
            }
        },
        {
            "name": "SENSEX Historical - BSE_EQ",
            "url": "https://api.dhan.co/v2/charts/historical",
            "payload": {
                "securityId": "51",
                "exchangeSegment": "BSE_EQ",
                "instrument": "INDEX",
                "fromDate": "2024-08-01",
                "toDate": "2024-08-01"
            }
        },
        {
            "name": "SENSEX Intraday - IDX_I",
            "url": "https://api.dhan.co/v2/charts/intraday",
            "payload": {
                "securityId": "51",
                "exchangeSegment": "IDX_I",
                "instrument": "INDEX",
                "interval": "5",
                "fromDate": "2024-08-01 09:15:00",
                "toDate": "2024-08-01 15:30:00"
            }
        },
        {
            "name": "SENSEX Historical - IDX_I",
            "url": "https://api.dhan.co/v2/charts/historical",
            "payload": {
                "securityId": "51",
                "exchangeSegment": "IDX_I",
                "instrument": "INDEX",
                "fromDate": "2024-08-01",
                "toDate": "2024-08-01"
            }
        }
    ]
    
    for i, config in enumerate(test_configs):
        print(f"\n--- Test {i+1}: {config['name']} ---")
        print(f"URL: {config['url']}")
        print(f"Payload: {json.dumps(config['payload'], indent=2)}")
        
        try:
            response = requests.post(config['url'], headers=headers, json=config['payload'])
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data and isinstance(data, dict):
                    print(f"✅ SUCCESS!")
                    print(f"Response keys: {list(data.keys())}")
                    
                    if 'open' in data and len(data['open']) > 0:
                        print(f"Data points: {len(data['open'])}")
                        print(f"Sample open price: {data['open'][0]}")
                        print(f"Sample close price: {data['close'][0] if 'close' in data else 'N/A'}")
                        
                        # This configuration works!
                        print(f"🎉 WORKING CONFIGURATION FOUND!")
                        return config
                    else:
                        print(f"⚠️ No OHLC data in response")
                else:
                    print(f"⚠️ Unexpected response format: {response.text[:200]}")
            else:
                print(f"❌ Error: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
        
        # Rate limiting
        time.sleep(0.2)
    
    print(f"\n❌ No working configuration found for SENSEX")
    return None

if __name__ == "__main__":
    working_config = test_sensex_endpoints()
    if working_config:
        print(f"\n🎯 Use this configuration:")
        print(f"URL: {working_config['url']}")
        print(f"Payload: {json.dumps(working_config['payload'], indent=2)}")
