import requests
import pandas as pd
import json
from datetime import datetime, timedelta

class DhanHistoricalData:
    def __init__(self):
        """Initialize the data fetcher with configurations"""
        self.api_config = {}
        self.date_config = {}
        self.load_configs()
        self.headers = {
            "access-token": self.api_config.get("access_token", ""),
            "client-id": self.api_config.get("client_id", ""),
            "Content-Type": "application/json"
        }
        
    def load_configs(self):
        """Load configuration files with error handling"""
        try:
            with open('dhan_config.json') as config_file:
                self.api_config = json.load(config_file)
            # Validate required fields
            if not all(k in self.api_config for k in ["client_id", "access_token"]):
                raise ValueError("Missing required fields in dhan_config.json")
        except Exception as e:
            print(f"Configuration error: {e}")
            exit(1)
        
        try:
            with open('date_range_config.json') as date_file:
                self.date_config = json.load(date_file)
            # Set defaults
            self.date_config.setdefault("interval", 5)
            self.date_config.setdefault("output_file", "nifty_ohlcv.csv")
        except Exception as e:
            print(f"Date config error: {e}")
            exit(1)
    
    def verify_credentials(self):
        """Verify API credentials using the correct profile endpoint"""
        profile_url = "https://api.dhan.co/v2/profile"
        try:
            response = requests.get(profile_url, headers=self.headers)
            if response.status_code == 200:
                print("Credentials verified successfully")
                return True
            print(f"Credential check failed. Status: {response.status_code}")
            print(f"Response: {response.text}")
            print("\nTroubleshooting steps:")
            print("1. Verify client_id and access_token in dhan_config.json")
            print("2. Check for trailing spaces in your credentials")
            print("3. Ensure your API subscription is active")
            return False
        except Exception as e:
            print(f"Connection error: {e}")
            return False
    
    def get_nifty_security_id(self):
        """Get the correct security ID for NIFTY 50"""
        # Try the configured ID first
        if "nifty_security_id" in self.api_config:
            if self.test_security_id(self.api_config["nifty_security_id"]):
                return self.api_config["nifty_security_id"]
        
        # Common Nifty ID formats to try
        common_ids = [
            "13",  # Keep the original ID first
            "NIFTY50_INDEX",
            "NIFTY 50",
            "NIFTY50",
            "NIFTY",
            "NSE:NIFTY50",
            "NIFTY_50_INDEX",
            "26000",  # Another common NIFTY ID
            "26009",  # NIFTY 50 index ID
            "NIFTY_50"
        ]
        
        for test_id in common_ids:
            print(f"Testing security ID: {test_id}")
            if self.test_security_id(test_id):
                print(f"✓ Using securityId: {test_id}")
                return test_id
            else:
                print(f"✗ Failed: {test_id}")
        
        print("\nFailed to identify Nifty security ID. Please:")
        print("1. Contact Dhan support for correct ID")
        print("2. Update dhan_config.json with 'nifty_security_id'")
        return None
    
    def test_security_id(self, security_id):
        """Test if a security ID works"""
        test_url = "https://api.dhan.co/v2/charts/historical"
        payload = {
            "securityId": security_id,
            "exchangeSegment": "IDX_I",  # Correct exchange segment for indices
            "instrument": "INDEX",
            "fromDate": datetime.now().strftime("%Y-%m-%d"),
            "toDate": datetime.now().strftime("%Y-%m-%d")
        }
        try:
            response = requests.post(test_url, headers=self.headers, json=payload)
            if response.status_code == 200:
                return True
            else:
                print(f"    Status: {response.status_code}, Response: {response.text[:100]}")
                return False
        except Exception as e:
            print(f"    Error: {e}")
            return False
    
    def fetch_intraday_data(self):
        """Fetch 5-minute OHLCV data"""
        url = "https://api.dhan.co/v2/charts/intraday"
        payload = {
            "securityId": self.api_config["nifty_security_id"],
            "exchangeSegment": "IDX_I",  # Correct exchange segment for indices
            "instrument": "INDEX",
            "interval": str(self.date_config["interval"]),  # Convert to string
            "fromDate": self.date_config["from_date"],
            "toDate": self.date_config["to_date"]
        }
        
        print("\nFetching data with parameters:")
        print(json.dumps(payload, indent=2))
        
        try:
            response = requests.post(url, headers=self.headers, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as err:
            print(f"\nAPI Error: {err}")
            print(f"Status: {err.response.status_code}")
            print(f"Response: {err.response.text}")
        except Exception as e:
            print(f"\nError: {e}")
        return None
    
    def process_data(self, raw_data):
        """Process the API response"""
        if not raw_data or not all(k in raw_data for k in ['open', 'high', 'low', 'close', 'volume', 'timestamp']):
            print("Invalid data format received")
            return None
            
        df = pd.DataFrame({
            'timestamp': raw_data['timestamp'],
            'open': raw_data['open'],
            'high': raw_data['high'],
            'low': raw_data['low'],
            'close': raw_data['close'],
            'volume': raw_data['volume']
        })
        
        # Convert to datetime with IST timezone
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s') + timedelta(hours=5, minutes=30)
        
        return df[['datetime', 'open', 'high', 'low', 'close', 'volume']]
    
    def save_to_csv(self, df):
        """Save data to CSV"""
        if df is not None and not df.empty:
            output_file = self.date_config["output_file"]
            df.to_csv(output_file, index=False)
            print(f"\nData saved to {output_file}")
            print(f"Time range: {df['datetime'].min()} to {df['datetime'].max()}")
            print(f"Records: {len(df)}")
        else:
            print("\nNo data was saved")
    
    def run(self):
        """Main execution flow"""
        print("\n===== Nifty Data Fetcher =====")

        if not self.verify_credentials():
            return

        # Use the security ID from config directly (we know "13" works)
        nifty_id = self.api_config.get("nifty_security_id", "13")
        print(f"\nUsing Nifty ID: {nifty_id}")

        data = self.fetch_intraday_data()
        processed = self.process_data(data)
        self.save_to_csv(processed)

        print("\n===== Complete =====")

if __name__ == "__main__":
    DhanHistoricalData().run()