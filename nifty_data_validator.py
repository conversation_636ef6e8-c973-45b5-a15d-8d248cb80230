import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import json
import time

class NiftyDataValidator:
    def __init__(self):
        """Initialize the data validator"""
        self.load_config()
        self.headers = {
            "access-token": self.api_config.get("access_token", ""),
            "client-id": self.api_config.get("client_id", ""),
            "Content-Type": "application/json"
        }
        
        # Trading hours and expected time slots
        self.trading_times = [
            "09:15", "09:20", "09:25", "09:30", "09:35", "09:40", "09:45", "09:50", "09:55",
            "10:00", "10:05", "10:10", "10:15", "10:20", "10:25", "10:30", "10:35", "10:40", "10:45", "10:50", "10:55",
            "11:00", "11:05", "11:10", "11:15", "11:20", "11:25", "11:30", "11:35", "11:40", "11:45", "11:50", "11:55",
            "12:00", "12:05", "12:10", "12:15", "12:20", "12:25", "12:30", "12:35", "12:40", "12:45", "12:50", "12:55",
            "13:00", "13:05", "13:10", "13:15", "13:20", "13:25", "13:30", "13:35", "13:40", "13:45", "13:50", "13:55",
            "14:00", "14:05", "14:10", "14:15", "14:20", "14:25", "14:30", "14:35", "14:40", "14:45", "14:50", "14:55",
            "15:00", "15:05", "15:10", "15:15", "15:20", "15:25", "15:30"
        ]
        
    def load_config(self):
        """Load API configuration"""
        try:
            with open('dhan_config.json') as config_file:
                self.api_config = json.load(config_file)
        except Exception as e:
            print(f"Configuration error: {e}")
            exit(1)
    
    def load_existing_data(self, filename):
        """Load and parse the existing CSV data"""
        print(f"📂 Loading existing data from {filename}...")
        
        try:
            df = pd.read_csv(filename)
            print(f"✅ Loaded {len(df):,} records")
            
            # Parse datetime properly
            df['datetime_parsed'] = pd.to_datetime(df['datetime'], format='%m/%d/%Y %H:%M')
            df['date_parsed'] = df['datetime_parsed'].dt.date
            df['time_parsed'] = df['datetime_parsed'].dt.strftime('%H:%M')
            
            return df
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return None
    
    def analyze_data_completeness(self, df):
        """Analyze the data for completeness and gaps"""
        print("\n🔍 ANALYZING DATA COMPLETENESS...")
        print("="*50)
        
        # Basic statistics
        min_date = df['date_parsed'].min()
        max_date = df['date_parsed'].max()
        total_days = (max_date - min_date).days + 1
        unique_dates = len(df['date_parsed'].unique())
        
        print(f"📅 Date Range: {min_date} to {max_date}")
        print(f"📊 Total Calendar Days: {total_days}")
        print(f"📈 Trading Days in Data: {unique_dates}")
        print(f"📋 Total Records: {len(df):,}")
        
        # Check for missing dates
        all_dates = pd.date_range(start=min_date, end=max_date, freq='D').date
        trading_dates_in_data = set(df['date_parsed'].unique())
        missing_dates = []
        
        for date in all_dates:
            # Skip weekends (Saturday=5, Sunday=6)
            if date.weekday() < 5:  # Monday=0 to Friday=4
                if date not in trading_dates_in_data:
                    missing_dates.append(date)
        
        print(f"\n🔍 MISSING TRADING DAYS ANALYSIS:")
        if missing_dates:
            print(f"⚠️ Found {len(missing_dates)} potentially missing trading days:")
            for i, date in enumerate(missing_dates[:10]):  # Show first 10
                print(f"   {i+1}. {date}")
            if len(missing_dates) > 10:
                print(f"   ... and {len(missing_dates) - 10} more")
        else:
            print("✅ No missing weekdays found")
        
        # Check time completeness for each trading day
        incomplete_days = []
        for date in trading_dates_in_data:
            day_data = df[df['date_parsed'] == date]
            times_in_day = set(day_data['time_parsed'].unique())
            
            missing_times = []
            for expected_time in self.trading_times:
                if expected_time not in times_in_day:
                    missing_times.append(expected_time)
            
            if missing_times:
                incomplete_days.append({
                    'date': date,
                    'missing_times': missing_times,
                    'records': len(day_data)
                })
        
        print(f"\n🕐 TIME COMPLETENESS ANALYSIS:")
        print(f"📊 Expected time slots per day: {len(self.trading_times)}")
        
        if incomplete_days:
            print(f"⚠️ Found {len(incomplete_days)} days with incomplete time data:")
            for i, day in enumerate(incomplete_days[:5]):  # Show first 5
                print(f"   {i+1}. {day['date']}: {day['records']} records, missing {len(day['missing_times'])} times")
            if len(incomplete_days) > 5:
                print(f"   ... and {len(incomplete_days) - 5} more incomplete days")
        else:
            print("✅ All trading days have complete time data")
        
        return {
            'missing_dates': missing_dates,
            'incomplete_days': incomplete_days,
            'date_range': (min_date, max_date),
            'total_records': len(df)
        }
    
    def fetch_missing_data(self, missing_dates, max_fetch=10):
        """Fetch data for missing dates with rate limiting"""
        if not missing_dates:
            print("✅ No missing dates to fetch")
            return []
        
        print(f"\n🔄 FETCHING MISSING DATA...")
        print(f"📦 Will attempt to fetch data for {min(len(missing_dates), max_fetch)} missing dates")
        
        fetched_data = []
        
        for i, date in enumerate(missing_dates[:max_fetch]):
            print(f"\n📅 Fetching data for {date} ({i+1}/{min(len(missing_dates), max_fetch)})")
            
            # Fetch data for this specific date
            url = "https://api.dhan.co/v2/charts/intraday"
            payload = {
                "securityId": "13",
                "exchangeSegment": "IDX_I",
                "instrument": "INDEX",
                "interval": "5",
                "fromDate": f"{date} 09:15:00",
                "toDate": f"{date} 15:30:00"
            }
            
            try:
                response = requests.post(url, headers=self.headers, json=payload)
                if response.status_code == 200:
                    data = response.json()
                    if data and 'open' in data and len(data['open']) > 0:
                        print(f"   ✅ Found {len(data['open'])} records")
                        
                        # Process the data
                        df_day = pd.DataFrame({
                            'timestamp': data['timestamp'],
                            'open': data['open'],
                            'high': data['high'],
                            'low': data['low'],
                            'close': data['close']
                        })
                        
                        # Convert to IST datetime
                        df_day['datetime_parsed'] = pd.to_datetime(df_day['timestamp'], unit='s') + timedelta(hours=5, minutes=30)
                        df_day['datetime'] = df_day['datetime_parsed'].dt.strftime('%m/%d/%Y %H:%M')
                        df_day['date'] = df_day['datetime_parsed'].dt.strftime('%m/%d/%Y')
                        df_day['time'] = df_day['datetime_parsed'].dt.strftime('%H:%M')
                        
                        # Select final columns to match existing format
                        df_final = df_day[['datetime', 'date', 'time', 'open', 'high', 'low', 'close']].copy()
                        fetched_data.append(df_final)
                        
                    else:
                        print(f"   ⚠️ No data available (likely holiday)")
                elif response.status_code == 429:
                    print(f"   ⏳ Rate limit hit, waiting 60 seconds...")
                    time.sleep(60)
                    continue
                else:
                    print(f"   ❌ API error: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
            
            # Rate limiting delay
            if i < min(len(missing_dates), max_fetch) - 1:
                print(f"   ⏳ Waiting 25 seconds (rate limiting)...")
                time.sleep(25)
        
        return fetched_data
    
    def update_data_file(self, original_df, new_data_list, filename):
        """Update the original file with new data"""
        if not new_data_list:
            print("✅ No new data to add")
            return
        
        print(f"\n💾 UPDATING DATA FILE...")
        
        # Combine all new data
        new_df = pd.concat(new_data_list, ignore_index=True)
        print(f"📊 Adding {len(new_df)} new records")
        
        # Combine with original data
        combined_df = pd.concat([original_df[['datetime', 'date', 'time', 'open', 'high', 'low', 'close']], new_df], ignore_index=True)
        
        # Remove duplicates and sort
        combined_df['datetime_for_sort'] = pd.to_datetime(combined_df['datetime'], format='%m/%d/%Y %H:%M')
        combined_df = combined_df.drop_duplicates(subset=['datetime'], keep='last')
        combined_df = combined_df.sort_values('datetime_for_sort').reset_index(drop=True)
        
        # Save updated file
        final_df = combined_df[['datetime', 'date', 'time', 'open', 'high', 'low', 'close']]
        final_df.to_csv(filename, index=False)
        
        print(f"✅ Updated file saved with {len(final_df):,} total records")
        
    def run_validation(self):
        """Main validation process"""
        print("\n" + "="*60)
        print("🔍 NIFTY DATA VALIDATION & GAP FILLING")
        print("="*60)
        
        filename = "nifty_complete_ohlc_2025.csv"
        
        # Load existing data
        df = self.load_existing_data(filename)
        if df is None:
            return
        
        # Analyze completeness
        analysis = self.analyze_data_completeness(df)
        
        # Check if we need to fetch missing data
        if analysis['missing_dates'] or analysis['incomplete_days']:
            print(f"\n🔧 DATA GAPS DETECTED!")
            
            if analysis['missing_dates']:
                print(f"📅 Missing dates: {len(analysis['missing_dates'])}")
                
                # Ask user if they want to fetch missing data
                print(f"\n🤔 Do you want to fetch missing data? This will take time due to rate limits.")
                print(f"   Will fetch up to 10 missing dates with 25-second delays between requests.")
                
                # Fetch missing data
                new_data = self.fetch_missing_data(analysis['missing_dates'], max_fetch=10)
                
                if new_data:
                    self.update_data_file(df, new_data, filename)
                    print(f"\n✅ Data validation and gap filling completed!")
                else:
                    print(f"\n⚠️ No additional data could be fetched")
            else:
                print(f"\n✅ No missing dates found, only incomplete time data on some days")
                print(f"   This is normal for holidays and partial trading days")
        else:
            print(f"\n🎉 DATA VALIDATION PASSED!")
            print(f"✅ All data appears complete and proper")
            print(f"📊 Total records: {analysis['total_records']:,}")
            print(f"📅 Date range: {analysis['date_range'][0]} to {analysis['date_range'][1]}")
        
        print("\n" + "="*60)
        print("✅ VALIDATION COMPLETE")
        print("="*60)

if __name__ == "__main__":
    validator = NiftyDataValidator()
    validator.run_validation()
