import requests
import pandas as pd
import json
from datetime import datetime, timedelta
import time
import os

class Nifty915OpeningPricesFetcher:
    def __init__(self):
        """Initialize the data fetcher for 9:15 AM opening prices only"""
        self.load_config()
        self.headers = {
            "access-token": self.api_config.get("access_token", ""),
            "client-id": self.api_config.get("client_id", ""),
            "Content-Type": "application/json"
        }
        
    def load_config(self):
        """Load API configuration"""
        try:
            with open('dhan_config.json') as config_file:
                self.api_config = json.load(config_file)
        except Exception as e:
            print(f"Configuration error: {e}")
            exit(1)
    
    def verify_credentials(self):
        """Verify API credentials"""
        profile_url = "https://api.dhan.co/v2/profile"
        try:
            response = requests.get(profile_url, headers=self.headers)
            return response.status_code == 200
        except:
            return False
    
    def generate_date_ranges(self, start_date, end_date):
        """Generate 45-day chunks for API requests (smaller chunks for better rate limiting)"""
        ranges = []
        current_date = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        while current_date < end_dt:
            # Use 45-day chunks to be very conservative with rate limits
            chunk_end = min(current_date + timedelta(days=44), end_dt)
            ranges.append({
                "from_date": current_date.strftime("%Y-%m-%d 09:15:00"),
                "to_date": chunk_end.strftime("%Y-%m-%d 09:20:00"),  # Only need 9:15 candle
                "start_date": current_date.strftime("%Y-%m-%d"),
                "end_date": chunk_end.strftime("%Y-%m-%d")
            })
            current_date = chunk_end + timedelta(days=1)
        
        return ranges
    
    def fetch_data_chunk(self, from_date, to_date):
        """Fetch data for a specific date range with enhanced retry logic"""
        url = "https://api.dhan.co/v2/charts/intraday"
        payload = {
            "securityId": "13",
            "exchangeSegment": "IDX_I",
            "instrument": "INDEX",
            "interval": "5",
            "fromDate": from_date,
            "toDate": to_date
        }
        
        max_retries = 5
        for attempt in range(max_retries):
            try:
                response = requests.post(url, headers=self.headers, json=payload)
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:  # Rate limit
                    wait_time = min(60 * (attempt + 1), 300)  # Progressive backoff, max 5 minutes
                    print(f"    Rate limit hit, waiting {wait_time} seconds...")
                    time.sleep(wait_time)
                    continue
                elif response.status_code == 400:
                    print(f"    Bad request (400) - possibly no data for this period")
                    return None
                else:
                    print(f"    API Error: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(15 * (attempt + 1))  # Progressive delay
                        continue
            except Exception as e:
                print(f"    Request error: {e}")
                if attempt < max_retries - 1:
                    time.sleep(15 * (attempt + 1))
                    continue
        
        return None
    
    def process_915_opening_data(self, raw_data):
        """Process raw data to extract only 9:15 AM opening prices"""
        if not raw_data or not all(k in raw_data for k in ['open', 'timestamp']):
            return None
            
        df = pd.DataFrame({
            'timestamp': raw_data['timestamp'],
            'open': raw_data['open']
        })
        
        # Convert to IST datetime
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s') + timedelta(hours=5, minutes=30)
        df['date'] = df['datetime'].dt.date
        df['time'] = df['datetime'].dt.strftime('%H:%M')
        
        # Filter only 9:15 AM candles
        df_915 = df[df['time'] == '09:15'].copy()
        
        if df_915.empty:
            return None
        
        # Create final dataframe with Date and Opening Price columns
        result_df = pd.DataFrame({
            'Date': df_915['date'],
            'Opening Price (9:15)': df_915['open']
        })
        
        # Remove duplicates and sort by date
        result_df = result_df.drop_duplicates(subset=['Date'], keep='first')
        result_df = result_df.sort_values('Date').reset_index(drop=True)
        
        return result_df
    
    def save_progress(self, data, filename):
        """Save progress to file"""
        if data is not None and not data.empty:
            data.to_csv(filename, index=False)
            return True
        return False
    
    def load_existing_progress(self, filename):
        """Load existing progress if available"""
        if os.path.exists(filename):
            try:
                return pd.read_csv(filename)
            except:
                return None
        return None
    
    def run(self):
        """Main execution flow"""
        print("\n===== NIFTY 9:15 AM Opening Prices Fetcher =====")
        print("Fetching OPENING PRICES of 9:15 AM candle ONLY")
        print("Time Period: January 2022 to August 2025")
        print("This will take some time due to conservative API rate limits.")
        
        if not self.verify_credentials():
            print("✗ Credential verification failed")
            return
        
        print("✓ Credentials verified")
        
        # Generate date ranges
        date_ranges = self.generate_date_ranges("2022-01-01", "2025-08-31")
        print(f"Will fetch data in {len(date_ranges)} chunks (45-day chunks)")
        
        # Check for existing progress
        final_output_file = "nifty_915_opening_prices_2022_2025.csv"
        progress_file = "nifty_915_progress.csv"
        
        existing_data = self.load_existing_progress(progress_file)
        if existing_data is not None:
            print(f"Found existing progress with {len(existing_data)} days")
            all_dataframes = [existing_data]
            # Find where to resume based on last date
            last_date = pd.to_datetime(existing_data['Date'].max()).date()
            start_chunk = 0
            for i, date_range in enumerate(date_ranges):
                range_start = datetime.strptime(date_range['start_date'], "%Y-%m-%d").date()
                if range_start > last_date:
                    start_chunk = i
                    break
        else:
            all_dataframes = []
            start_chunk = 0
        
        total_days_fetched = len(existing_data) if existing_data is not None else 0
        
        for i in range(start_chunk, len(date_ranges)):
            date_range = date_ranges[i]
            print(f"\nChunk {i+1}/{len(date_ranges)}: {date_range['start_date']} to {date_range['end_date']}")
            
            raw_data = self.fetch_data_chunk(date_range['from_date'], date_range['to_date'])
            if raw_data:
                processed_df = self.process_915_opening_data(raw_data)
                if processed_df is not None and not processed_df.empty:
                    all_dataframes.append(processed_df)
                    total_days_fetched += len(processed_df)
                    print(f"✓ Got {len(processed_df)} trading days (Total: {total_days_fetched})")
                else:
                    print("✗ No 9:15 AM data in this chunk")
            else:
                print("✗ Failed to fetch data for this chunk")
            
            # Save progress every 3 chunks (more frequent saves)
            if (i + 1) % 3 == 0 and all_dataframes:
                combined_data = pd.concat(all_dataframes, ignore_index=True)
                combined_data = combined_data.drop_duplicates(subset=['Date'], keep='last')
                combined_data = combined_data.sort_values('Date').reset_index(drop=True)
                self.save_progress(combined_data, progress_file)
                print(f"Progress saved: {len(combined_data)} days")
            
            # Conservative delay to avoid rate limits
            print("Waiting 15 seconds to avoid rate limits...")
            time.sleep(15)
        
        if not all_dataframes:
            print("No data was fetched!")
            return
        
        # Combine all data and remove duplicates
        print("\nCombining all data...")
        combined_data = pd.concat(all_dataframes, ignore_index=True)
        combined_data = combined_data.drop_duplicates(subset=['Date'], keep='last')
        combined_data = combined_data.sort_values('Date').reset_index(drop=True)
        
        # Save final file
        combined_data.to_csv(final_output_file, index=False)
        
        print(f"\n✅ SUCCESS! Data saved to {final_output_file}")
        print(f"📅 Date range: {combined_data['Date'].min()} to {combined_data['Date'].max()}")
        print(f"📊 Total trading days: {len(combined_data)}")
        print(f"📈 Format: Date + Opening Price (9:15 AM candle only)")
        
        # Show sample data
        print(f"\n📋 Sample data (first 5 rows):")
        print(combined_data.head().to_string(index=False))
        
        print("\n===== Complete =====")

if __name__ == "__main__":
    Nifty915OpeningPricesFetcher().run()
