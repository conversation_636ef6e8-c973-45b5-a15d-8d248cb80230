import requests
import pandas as pd
import json
from datetime import datetime, timedelta
import time

class NiftyOpeningPricesFetcher:
    def __init__(self):
        """Initialize the data fetcher"""
        self.load_config()
        self.headers = {
            "access-token": self.api_config.get("access_token", ""),
            "client-id": self.api_config.get("client_id", ""),
            "Content-Type": "application/json"
        }
        
        # Define the time slots for opening prices
        self.time_slots = [
            "9:15", "9:20", "9:25", "9:30", "9:35", "9:40", 
            "9:45", "9:50", "9:55", "10:00", "10:05", "10:10",
            "10:15", "10:20", "10:25", "10:30", "10:35", "10:40",
            "10:45", "10:50", "10:55", "11:00", "11:05", "11:10",
            "11:15", "11:20", "11:25", "11:30", "11:35", "11:40",
            "11:45", "11:50", "11:55", "12:00", "12:05", "12:10",
            "12:15", "12:20", "12:25", "12:30", "12:35", "12:40",
            "12:45", "12:50", "12:55", "13:00", "13:05", "13:10",
            "13:15", "13:20", "13:25", "13:30", "13:35", "13:40",
            "13:45", "13:50", "13:55", "14:00", "14:05", "14:10",
            "14:15", "14:20", "14:25", "14:30", "14:35", "14:40",
            "14:45", "14:50", "14:55", "15:00", "15:05", "15:10",
            "15:15", "15:20", "15:25", "15:30"
        ]
        
    def load_config(self):
        """Load API configuration"""
        try:
            with open('dhan_config.json') as config_file:
                self.api_config = json.load(config_file)
        except Exception as e:
            print(f"Configuration error: {e}")
            exit(1)
    
    def verify_credentials(self):
        """Verify API credentials"""
        profile_url = "https://api.dhan.co/v2/profile"
        try:
            response = requests.get(profile_url, headers=self.headers)
            if response.status_code == 200:
                print("✓ Credentials verified successfully")
                return True
            print(f"✗ Credential check failed. Status: {response.status_code}")
            return False
        except Exception as e:
            print(f"Connection error: {e}")
            return False
    
    def generate_date_ranges(self, start_date, end_date):
        """Generate 90-day chunks for API requests"""
        ranges = []
        current_date = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        while current_date < end_dt:
            chunk_end = min(current_date + timedelta(days=89), end_dt)
            ranges.append({
                "from_date": current_date.strftime("%Y-%m-%d 09:15:00"),
                "to_date": chunk_end.strftime("%Y-%m-%d 15:30:00")
            })
            current_date = chunk_end + timedelta(days=1)
        
        return ranges
    
    def fetch_data_chunk(self, from_date, to_date):
        """Fetch data for a specific date range"""
        url = "https://api.dhan.co/v2/charts/intraday"
        payload = {
            "securityId": "13",
            "exchangeSegment": "IDX_I",
            "instrument": "INDEX",
            "interval": "5",
            "fromDate": from_date,
            "toDate": to_date
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=payload)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"API Error: {response.status_code} - {response.text[:100]}")
                return None
        except Exception as e:
            print(f"Request error: {e}")
            return None
    
    def process_raw_data(self, raw_data):
        """Convert raw API data to DataFrame"""
        if not raw_data or not all(k in raw_data for k in ['open', 'timestamp']):
            return None
            
        df = pd.DataFrame({
            'timestamp': raw_data['timestamp'],
            'open': raw_data['open']
        })
        
        # Convert to IST datetime
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s') + timedelta(hours=5, minutes=30)
        df['date'] = df['datetime'].dt.date
        df['time'] = df['datetime'].dt.strftime('%H:%M')
        
        return df[['date', 'time', 'open']]
    
    def format_data_for_excel(self, all_data):
        """Format data in the requested Excel format"""
        if all_data.empty:
            return None

        # Create pivot table with dates as rows and times as columns
        pivot_df = all_data.pivot_table(
            index='date',
            columns='time',
            values='open',
            aggfunc='first'
        )

        # Reset index to make date a column
        pivot_df = pivot_df.reset_index()

        # Rename columns to match your exact requirement
        column_mapping = {'date': 'Date'}

        # First column should be "Opening Price (9:15)"
        if '09:15' in pivot_df.columns:
            column_mapping['09:15'] = 'Opening Price (9:15)'

        # All other columns should just be the time (9:20, 9:25, etc.)
        time_columns = ['09:20', '09:25', '09:30', '09:35', '09:40', '09:45', '09:50', '09:55', '10:00']
        for time_col in time_columns:
            if time_col in pivot_df.columns:
                column_mapping[time_col] = time_col.lstrip('0').replace(':', ':')

        # For times after 10:00, keep the format as is
        for col in pivot_df.columns:
            if col not in column_mapping and col != 'date':
                if col.startswith('10:') or col.startswith('11:') or col.startswith('12:') or col.startswith('13:') or col.startswith('14:') or col.startswith('15:'):
                    column_mapping[col] = col

        pivot_df = pivot_df.rename(columns=column_mapping)

        return pivot_df
    
    def run(self):
        """Main execution flow"""
        print("\n===== NIFTY Opening Prices Fetcher =====")
        print("Fetching data from January 2022 to July 2025...")
        
        if not self.verify_credentials():
            return
        
        # Generate date ranges - Testing with smaller range first
        date_ranges = self.generate_date_ranges("2025-08-01", "2025-08-08")
        print(f"Will fetch data in {len(date_ranges)} chunks...")
        
        all_dataframes = []
        
        for i, date_range in enumerate(date_ranges):
            print(f"\nFetching chunk {i+1}/{len(date_ranges)}: {date_range['from_date'][:10]} to {date_range['to_date'][:10]}")
            
            raw_data = self.fetch_data_chunk(date_range['from_date'], date_range['to_date'])
            if raw_data:
                processed_df = self.process_raw_data(raw_data)
                if processed_df is not None and not processed_df.empty:
                    all_dataframes.append(processed_df)
                    print(f"✓ Got {len(processed_df)} records")
                else:
                    print("✗ No data in this chunk")
            else:
                print("✗ Failed to fetch data for this chunk")
            
            # Add delay to avoid rate limiting
            if i < len(date_ranges) - 1:
                time.sleep(2)
        
        if not all_dataframes:
            print("No data was fetched!")
            return
        
        # Combine all data
        combined_data = pd.concat(all_dataframes, ignore_index=True)
        print(f"\nTotal records fetched: {len(combined_data)}")
        
        # Format for Excel
        formatted_data = self.format_data_for_excel(combined_data)
        
        if formatted_data is not None:
            # Save to CSV
            output_file = "nifty_opening_prices_2022_2025.csv"
            formatted_data.to_csv(output_file, index=False)
            print(f"\n✓ Data saved to {output_file}")
            print(f"Date range: {formatted_data['Date'].min()} to {formatted_data['Date'].max()}")
            print(f"Total trading days: {len(formatted_data)}")
        else:
            print("Failed to format data")
        
        print("\n===== Complete =====")

if __name__ == "__main__":
    NiftyOpeningPricesFetcher().run()
