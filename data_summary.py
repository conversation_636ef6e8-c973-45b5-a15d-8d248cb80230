import pandas as pd

# Load the CSV file
df = pd.read_csv('nifty_5min_ohlcv.csv')

print("===== NIFTY 50 - 5 Minute OHLCV Data Summary =====")
print(f"Data Range: {df['datetime'].min()} to {df['datetime'].max()}")
print(f"Total Records: {len(df)}")
print(f"Total Days: {len(df['datetime'].str[:10].unique())}")

print("\n===== Price Statistics =====")
print(f"Highest Price: {df['high'].max():.2f}")
print(f"Lowest Price: {df['low'].min():.2f}")
print(f"Opening Price (First): {df['open'].iloc[0]:.2f}")
print(f"Closing Price (Last): {df['close'].iloc[-1]:.2f}")
print(f"Price Change: {df['close'].iloc[-1] - df['open'].iloc[0]:.2f}")
print(f"Price Change %: {((df['close'].iloc[-1] - df['open'].iloc[0]) / df['open'].iloc[0] * 100):.2f}%")

print("\n===== Sample Data (First 5 rows) =====")
print(df.head().to_string(index=False))

print("\n===== Data Types =====")
print(df.dtypes)

print("\n✓ Data successfully saved to 'nifty_5min_ohlcv.csv'")
print("✓ Ready for analysis and trading strategies!")
