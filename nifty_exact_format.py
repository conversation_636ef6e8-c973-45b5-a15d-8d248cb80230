import requests
import pandas as pd
import json
from datetime import datetime, timedelta
import time

class NiftyExactFormatFetcher:
    def __init__(self):
        """Initialize the data fetcher"""
        self.load_config()
        self.headers = {
            "access-token": self.api_config.get("access_token", ""),
            "client-id": self.api_config.get("client_id", ""),
            "Content-Type": "application/json"
        }
        
    def load_config(self):
        """Load API configuration"""
        try:
            with open('dhan_config.json') as config_file:
                self.api_config = json.load(config_file)
        except Exception as e:
            print(f"Configuration error: {e}")
            exit(1)
    
    def verify_credentials(self):
        """Verify API credentials"""
        profile_url = "https://api.dhan.co/v2/profile"
        try:
            response = requests.get(profile_url, headers=self.headers)
            return response.status_code == 200
        except:
            return False
    
    def generate_date_ranges(self, start_date, end_date):
        """Generate 90-day chunks for API requests"""
        ranges = []
        current_date = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        while current_date < end_dt:
            chunk_end = min(current_date + timedelta(days=89), end_dt)
            ranges.append({
                "from_date": current_date.strftime("%Y-%m-%d 09:15:00"),
                "to_date": chunk_end.strftime("%Y-%m-%d 15:30:00")
            })
            current_date = chunk_end + timedelta(days=1)
        
        return ranges
    
    def fetch_data_chunk(self, from_date, to_date):
        """Fetch data for a specific date range"""
        url = "https://api.dhan.co/v2/charts/intraday"
        payload = {
            "securityId": "13",
            "exchangeSegment": "IDX_I",
            "instrument": "INDEX",
            "interval": "5",
            "fromDate": from_date,
            "toDate": to_date
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=payload)
            if response.status_code == 200:
                return response.json()
            return None
        except:
            return None
    
    def process_and_format_data(self, raw_data):
        """Process raw data and format exactly as requested"""
        if not raw_data or not all(k in raw_data for k in ['open', 'timestamp']):
            return None
            
        df = pd.DataFrame({
            'timestamp': raw_data['timestamp'],
            'open': raw_data['open']
        })
        
        # Convert to IST datetime
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s') + timedelta(hours=5, minutes=30)
        df['date'] = df['datetime'].dt.date
        df['time'] = df['datetime'].dt.strftime('%H:%M')
        
        # Create pivot table
        pivot_df = df.pivot_table(index='date', columns='time', values='open', aggfunc='first')
        pivot_df = pivot_df.reset_index()
        
        # Create the exact column structure you requested
        final_columns = ['Date', 'Opening Price (9:15)', '9:20', '9:25', '9:30', '9:35', '9:40', '9:45', '9:50', '9:55', '10:00']
        
        # Create final dataframe with exact columns
        final_df = pd.DataFrame()
        final_df['Date'] = pivot_df['date']
        
        # Map the time columns to your exact format
        time_mapping = {
            '09:15': 'Opening Price (9:15)',
            '09:20': '9:20',
            '09:25': '9:25', 
            '09:30': '9:30',
            '09:35': '9:35',
            '09:40': '9:40',
            '09:45': '9:45',
            '09:50': '9:50',
            '09:55': '9:55',
            '10:00': '10:00'
        }
        
        for time_col, final_col in time_mapping.items():
            if time_col in pivot_df.columns:
                final_df[final_col] = pivot_df[time_col]
            else:
                final_df[final_col] = None
        
        return final_df
    
    def run(self):
        """Main execution flow"""
        print("\n===== NIFTY Opening Prices (Exact Format) =====")
        
        if not self.verify_credentials():
            print("✗ Credential verification failed")
            return
        
        print("✓ Credentials verified")
        
        # Full date range as requested: January 2022 to July 2025
        date_ranges = self.generate_date_ranges("2022-01-01", "2025-07-31")
        print(f"Will fetch data in {len(date_ranges)} chunks...")
        
        all_dataframes = []
        
        for i, date_range in enumerate(date_ranges):
            print(f"Fetching chunk {i+1}/{len(date_ranges)}: {date_range['from_date'][:10]} to {date_range['to_date'][:10]}")

            # Try up to 3 times for each chunk
            for attempt in range(3):
                raw_data = self.fetch_data_chunk(date_range['from_date'], date_range['to_date'])
                if raw_data:
                    processed_df = self.process_and_format_data(raw_data)
                    if processed_df is not None and not processed_df.empty:
                        all_dataframes.append(processed_df)
                        print(f"✓ Got {len(processed_df)} trading days")
                        break
                    else:
                        print(f"✗ No data in chunk (attempt {attempt+1})")
                else:
                    print(f"✗ Failed to fetch chunk (attempt {attempt+1})")

                if attempt < 2:  # Don't sleep after last attempt
                    time.sleep(5)  # Wait longer between retries

            # Add delay to avoid rate limiting
            if i < len(date_ranges) - 1:
                time.sleep(3)  # Increased delay

            # Save progress every 10 chunks
            if (i + 1) % 10 == 0 and all_dataframes:
                temp_data = pd.concat(all_dataframes, ignore_index=True)
                temp_file = f"nifty_progress_{i+1}_chunks.csv"
                temp_data.to_csv(temp_file, index=False)
                print(f"Progress saved to {temp_file}")
        
        if not all_dataframes:
            print("No data was fetched!")
            return
        
        # Combine all data
        combined_data = pd.concat(all_dataframes, ignore_index=True)
        
        # Save to CSV
        output_file = "nifty_opening_prices_exact_format.csv"
        combined_data.to_csv(output_file, index=False)
        
        print(f"\n✓ Data saved to {output_file}")
        print(f"Date range: {combined_data['Date'].min()} to {combined_data['Date'].max()}")
        print(f"Total trading days: {len(combined_data)}")
        print("\n===== Complete =====")

if __name__ == "__main__":
    NiftyExactFormatFetcher().run()
