import requests
import pandas as pd
import json
from datetime import datetime, timedelta
import time
import os

class FinNiftyCompleteOHLCFetcher:
    def __init__(self):
        """Initialize the FINNIFTY OHLC data fetcher"""
        self.load_config()
        self.headers = {
            "access-token": self.api_config.get("access_token", ""),
            "client-id": self.api_config.get("client_id", ""),
            "Content-Type": "application/json"
        }
        
        # API rate limiting: 10 requests per second max
        # We'll use 6.67 requests per second to be safe (0.15 seconds between requests)
        self.request_delay = 0.15  # 150ms between requests
        self.chunk_size_days = 45  # 45-day chunks for better management
        self.max_retries = 3
        
        # Common FINNIFTY security IDs to test
        self.finnifty_ids = [
            "27",          # Most likely FINNIFTY ID
            "28",          # Alternative ID
            "FINNIFTY",
            "FIN NIFTY",
            "FINNIFTY_INDEX",
            "NIFTY FIN",
            "NIFTY FINANCIAL",
            "27000",       # Another possible ID
            "28000",       # Another possible ID
            "FINIFTY",     # Alternative spelling
            "26002",       # Sequential ID
            "26003"        # Sequential ID
        ]
        
    def load_config(self):
        """Load API configuration"""
        try:
            with open('dhan_config.json') as config_file:
                self.api_config = json.load(config_file)
        except Exception as e:
            print(f"Configuration error: {e}")
            exit(1)
    
    def verify_credentials(self):
        """Verify API credentials"""
        profile_url = "https://api.dhan.co/v2/profile"
        try:
            response = requests.get(profile_url, headers=self.headers)
            if response.status_code == 200:
                print("✓ Credentials verified successfully")
                return True
            else:
                print(f"✗ Credential verification failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ Connection error: {e}")
            return False
    
    def find_finnifty_security_id(self):
        """Find the correct security ID for FINNIFTY"""
        print("🔍 Finding FINNIFTY security ID...")
        
        test_date = "2024-08-01"  # Recent date for testing
        
        for security_id in self.finnifty_ids:
            print(f"  Testing security ID: {security_id}")
            
            url = "https://api.dhan.co/v2/charts/intraday"
            payload = {
                "securityId": security_id,
                "exchangeSegment": "IDX_I",
                "instrument": "INDEX",
                "interval": "5",
                "fromDate": f"{test_date} 09:15:00",
                "toDate": f"{test_date} 15:30:00"
            }
            
            try:
                response = requests.post(url, headers=self.headers, json=payload)
                if response.status_code == 200:
                    data = response.json()
                    if data and 'open' in data and len(data['open']) > 0:
                        # Verify it's FINNIFTY by checking price range (typically 15,000-25,000)
                        sample_price = data['open'][0]
                        if 10000 < sample_price < 30000:  # FINNIFTY price range
                            print(f"  ✓ Found FINNIFTY with ID: {security_id}")
                            print(f"    Sample data points: {len(data['open'])}")
                            print(f"    Sample price: {sample_price}")
                            return security_id
                        else:
                            print(f"  ⚠️ Price {sample_price} doesn't match FINNIFTY range")
                    else:
                        print(f"  ✗ No data for ID: {security_id}")
                elif response.status_code == 429:
                    print(f"  ⏳ Rate limit hit, waiting...")
                    time.sleep(2)
                    continue
                else:
                    print(f"  ✗ API error for ID {security_id}: {response.status_code}")
                
                time.sleep(self.request_delay)  # Rate limiting
                
            except Exception as e:
                print(f"  ✗ Error testing ID {security_id}: {e}")
                time.sleep(self.request_delay)
        
        print("❌ Could not find FINNIFTY security ID")
        return None
    
    def find_earliest_available_date(self, security_id):
        """Find the earliest date available for FINNIFTY"""
        print("🔍 Finding earliest available date for FINNIFTY...")
        
        # FINNIFTY was launched later than NIFTY/Bank NIFTY, so test more recent dates
        test_dates = [
            "2017-01-01", "2018-01-01", "2019-01-01", "2020-01-01", 
            "2021-01-01", "2022-01-01", "2023-01-01"
        ]
        
        earliest_date = None
        
        for test_date in test_dates:
            print(f"  Testing date: {test_date}")
            
            url = "https://api.dhan.co/v2/charts/intraday"
            payload = {
                "securityId": security_id,
                "exchangeSegment": "IDX_I",
                "instrument": "INDEX",
                "interval": "5",
                "fromDate": f"{test_date} 09:15:00",
                "toDate": f"{test_date} 15:30:00"
            }
            
            try:
                response = requests.post(url, headers=self.headers, json=payload)
                if response.status_code == 200:
                    data = response.json()
                    if data and 'timestamp' in data and len(data['timestamp']) > 0:
                        earliest_date = test_date
                        print(f"  ✓ Data available from {test_date}")
                        break
                    else:
                        print(f"  ✗ No data for {test_date}")
                elif response.status_code == 429:
                    print(f"  ⏳ Rate limit hit, waiting...")
                    time.sleep(2)
                    continue
                else:
                    print(f"  ✗ API error for {test_date}: {response.status_code}")
                
                time.sleep(self.request_delay)
                
            except Exception as e:
                print(f"  ✗ Error testing {test_date}: {e}")
                time.sleep(self.request_delay)
        
        if earliest_date:
            print(f"✅ Earliest available date found: {earliest_date}")
            return earliest_date
        else:
            print("⚠️ Could not determine earliest date, defaulting to 2022-01-01")
            return "2022-01-01"
    
    def generate_date_ranges(self, start_date, end_date):
        """Generate date chunks for API requests"""
        ranges = []
        current_date = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        chunk_number = 1
        while current_date < end_dt:
            chunk_end = min(current_date + timedelta(days=self.chunk_size_days - 1), end_dt)
            ranges.append({
                "chunk_id": chunk_number,
                "from_date": current_date.strftime("%Y-%m-%d 09:15:00"),
                "to_date": chunk_end.strftime("%Y-%m-%d 15:30:00"),
                "start_date": current_date.strftime("%Y-%m-%d"),
                "end_date": chunk_end.strftime("%Y-%m-%d")
            })
            current_date = chunk_end + timedelta(days=1)
            chunk_number += 1
        
        return ranges
    
    def fetch_data_chunk_with_rate_limit(self, chunk_info, security_id):
        """Fetch data with proper rate limiting (10 req/sec max)"""
        url = "https://api.dhan.co/v2/charts/intraday"
        payload = {
            "securityId": security_id,
            "exchangeSegment": "IDX_I",
            "instrument": "INDEX",
            "interval": "5",
            "fromDate": chunk_info["from_date"],
            "toDate": chunk_info["to_date"]
        }
        
        for attempt in range(self.max_retries):
            try:
                print(f"    Attempt {attempt + 1}/{self.max_retries}")
                
                # Rate limiting: Wait before request
                time.sleep(self.request_delay)
                
                response = requests.post(url, headers=self.headers, json=payload)
                
                if response.status_code == 200:
                    data = response.json()
                    if data and 'open' in data and len(data['open']) > 0:
                        print(f"    ✓ Success: {len(data['open'])} data points")
                        return data
                    else:
                        print(f"    ⚠️ Empty response (no trading data for this period)")
                        return None
                        
                elif response.status_code == 429:  # Rate limit
                    wait_time = 2 ** attempt  # Exponential backoff: 1, 2, 4 seconds
                    print(f"    ⏳ Rate limit hit, waiting {wait_time} seconds...")
                    time.sleep(wait_time)
                    continue
                    
                elif response.status_code == 400:
                    print(f"    ⚠️ Bad request (400) - possibly no data for this period")
                    return None
                    
                else:
                    print(f"    ✗ API Error: {response.status_code}")
                    if attempt < self.max_retries - 1:
                        time.sleep(1 * (attempt + 1))
                        continue
                        
            except Exception as e:
                print(f"    ✗ Request error: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1 * (attempt + 1))
                    continue
        
        print(f"    ✗ Failed after {self.max_retries} attempts")
        return None
    
    def process_ohlc_data(self, raw_data):
        """Process raw API data into OHLC format"""
        if not raw_data or not all(k in raw_data for k in ['open', 'high', 'low', 'close', 'volume', 'timestamp']):
            return None
            
        df = pd.DataFrame({
            'timestamp': raw_data['timestamp'],
            'open': raw_data['open'],
            'high': raw_data['high'],
            'low': raw_data['low'],
            'close': raw_data['close'],
            'volume': raw_data['volume']
        })
        
        # Convert to IST datetime
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s') + timedelta(hours=5, minutes=30)
        df['date'] = df['datetime'].dt.date
        df['time'] = df['datetime'].dt.strftime('%H:%M')
        
        # Select final columns
        result_df = df[['datetime', 'date', 'time', 'open', 'high', 'low', 'close', 'volume']].copy()
        result_df = result_df.sort_values('datetime').reset_index(drop=True)
        
        return result_df
    
    def save_progress(self, data, filename):
        """Save progress to file"""
        if data is not None and not data.empty:
            data.to_csv(filename, index=False)
            return True
        return False
    
    def load_existing_progress(self, filename):
        """Load existing progress if available"""
        if os.path.exists(filename):
            try:
                df = pd.read_csv(filename)
                if not df.empty:
                    return df
            except Exception as e:
                print(f"Error loading progress file: {e}")
        return None
    
    def run(self):
        """Main execution flow"""
        print("\n" + "="*60)
        print("💰 FINNIFTY COMPLETE OHLC DATA FETCHER")
        print("="*60)
        print("📊 Data: Complete OHLC + Volume")
        print("⏰ Timeframe: 5-minute candles")
        print("📅 Period: From earliest available date to August 10, 2025")
        print("🔒 Rate Limiting: 6.67 requests/second (within 10 req/sec limit)")
        print("="*60)
        
        if not self.verify_credentials():
            return
        
        # Find FINNIFTY security ID
        security_id = self.find_finnifty_security_id()
        if not security_id:
            print("❌ Could not find FINNIFTY security ID. Exiting.")
            return
        
        # Find earliest available date
        earliest_date = self.find_earliest_available_date(security_id)
        end_date = "2025-08-10"
        
        print(f"\n📅 Data Range: {earliest_date} to {end_date}")
        print(f"💰 FINNIFTY Security ID: {security_id}")
        
        # Generate date ranges
        date_ranges = self.generate_date_ranges(earliest_date, end_date)
        print(f"📦 Total chunks to process: {len(date_ranges)} ({self.chunk_size_days}-day chunks)")
        
        # File management
        final_output_file = "finnifty_complete_ohlc_2025.csv"
        progress_file = "finnifty_ohlc_progress.csv"
        
        # Check for existing progress
        existing_data = self.load_existing_progress(progress_file)
        if existing_data is not None:
            print(f"📂 Found existing progress: {len(existing_data)} records")
            all_dataframes = [existing_data]
            # Find resume point
            last_datetime = pd.to_datetime(existing_data['datetime'].max())
            last_date = last_datetime.date()
            start_chunk = 0
            for i, chunk in enumerate(date_ranges):
                chunk_start = datetime.strptime(chunk['start_date'], "%Y-%m-%d").date()
                if chunk_start > last_date:
                    start_chunk = i
                    break
            print(f"📍 Resuming from chunk {start_chunk + 1}")
        else:
            all_dataframes = []
            start_chunk = 0
            print("🆕 Starting fresh data collection")
        
        total_records = len(existing_data) if existing_data is not None else 0
        
        # Main data fetching loop
        print(f"\n🔄 Starting data collection...")
        
        for i in range(start_chunk, len(date_ranges)):
            chunk = date_ranges[i]
            
            print(f"\n📦 Chunk {chunk['chunk_id']}/{len(date_ranges)}: {chunk['start_date']} to {chunk['end_date']}")
            
            # Fetch data for this chunk
            raw_data = self.fetch_data_chunk_with_rate_limit(chunk, security_id)
            
            if raw_data:
                processed_df = self.process_ohlc_data(raw_data)
                if processed_df is not None and not processed_df.empty:
                    all_dataframes.append(processed_df)
                    total_records += len(processed_df)
                    print(f"    ✅ Added {len(processed_df)} records (Total: {total_records})")
                else:
                    print(f"    ⚠️ No processable data in this chunk")
            else:
                print(f"    ❌ Failed to fetch data for this chunk")
            
            # Save progress every 3 chunks
            if (i + 1) % 3 == 0 and all_dataframes:
                print(f"    💾 Saving progress...")
                combined_data = pd.concat(all_dataframes, ignore_index=True)
                combined_data = combined_data.drop_duplicates(subset=['datetime'], keep='last')
                combined_data = combined_data.sort_values('datetime').reset_index(drop=True)
                self.save_progress(combined_data, progress_file)
                print(f"    ✅ Progress saved: {len(combined_data)} total records")
        
        # Final processing
        if not all_dataframes:
            print("\n❌ No data was collected!")
            return
        
        print(f"\n🔄 Final processing...")
        combined_data = pd.concat(all_dataframes, ignore_index=True)
        combined_data = combined_data.drop_duplicates(subset=['datetime'], keep='last')
        combined_data = combined_data.sort_values('datetime').reset_index(drop=True)
        
        # Save final file
        combined_data.to_csv(final_output_file, index=False)
        
        # Success summary
        print(f"\n" + "="*60)
        print("🎉 FINNIFTY DATA COLLECTION COMPLETED!")
        print("="*60)
        print(f"📁 File: {final_output_file}")
        print(f"📅 Date Range: {combined_data['datetime'].min()} to {combined_data['datetime'].max()}")
        print(f"📊 Total Records: {len(combined_data):,}")
        print(f"📈 Columns: {list(combined_data.columns)}")
        print(f"🗓️ Trading Days: {len(combined_data['date'].unique())}")
        
        # Show sample data
        print(f"\n📋 Sample Data (first 5 rows):")
        print(combined_data.head().to_string(index=False))
        
        print(f"\n📋 Sample Data (last 5 rows):")
        print(combined_data.tail().to_string(index=False))
        
        print("\n" + "="*60)
        print("✅ COMPLETE FINNIFTY OHLC DATA READY!")
        print("="*60)

if __name__ == "__main__":
    FinNiftyCompleteOHLCFetcher().run()
